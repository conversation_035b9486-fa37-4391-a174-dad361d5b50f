import { Controller, All, Param, Req, UseGuards } from '@nestjs/common';
import { ApiT<PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import { ProxyService } from './proxy.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Proxy')
@Controller()
export class ProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  @All('management/*')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async proxyToManagementService(@Req() request: Request) {
    return this.proxyService.forwardToService('MANAGEMENT_SERVICE', request);
  }

  @All('correspondence/*')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async proxyToCorrespondenceService(@Req() request: Request) {
    return this.proxyService.forwardToService('CORRESPONDENCE_SERVICE', request);
  }
}
