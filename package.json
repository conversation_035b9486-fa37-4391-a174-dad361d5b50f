{"name": "enterprise-microservices-backend", "version": "1.0.0", "description": "Enterprise microservices backend with NestJS, TypeScript, and EventStore", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "clean": "turbo run clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "start:dev": "concurrently \"npm run start:gateway\" \"npm run start:management\" \"npm run start:correspondence\"", "start:gateway": "cd apps/api-gateway && npm run start:dev", "start:management": "cd apps/management-center && npm run start:dev", "start:correspondence": "cd apps/correspondence && npm run start:dev"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "turbo": "^1.10.0", "typescript": "^5.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}