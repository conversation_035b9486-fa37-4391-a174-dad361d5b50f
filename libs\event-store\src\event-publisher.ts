import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { DomainEvent, EventPublisher } from '@enterprise/common';

@Injectable()
export class EventStorePublisher implements EventPublisher {
  private readonly logger = new Logger(EventStorePublisher.name);

  constructor(private readonly eventBus: EventBus) {}

  async publish(event: DomainEvent): Promise<void> {
    try {
      this.eventBus.publish(event);
      this.logger.log(`Published event ${event.eventType} for aggregate ${event.aggregateId}`);
    } catch (error) {
      this.logger.error(`Failed to publish event ${event.eventType}`, error);
      throw error;
    }
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    try {
      this.eventBus.publishAll(events);
      this.logger.log(`Published ${events.length} events`);
    } catch (error) {
      this.logger.error(`Failed to publish ${events.length} events`, error);
      throw error;
    }
  }
}
