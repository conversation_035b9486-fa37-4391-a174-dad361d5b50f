import { Controller, Get, Post, Body, Param, Put, Delete, Query } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiResponseDto, PaginationDto } from '@enterprise/common';
import { CreateUserCommand } from './commands/create-user.command';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  async create(@Body() createUserDto: CreateUserDto) {
    const command = new CreateUserCommand(createUserDto);
    const result = await this.commandBus.execute(command);
    return ApiResponseDto.success(result, 'User created successfully');
  }

  @Get()
  @ApiOperation({ summary: 'Get all users' })
  async findAll(@Query() pagination: PaginationDto) {
    const result = await this.usersService.findAll(pagination);
    return ApiResponseDto.success(result, 'Users retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  async findOne(@Param('id') id: string) {
    const result = await this.usersService.findOne(id);
    return ApiResponseDto.success(result, 'User retrieved successfully');
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update user' })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    const result = await this.usersService.update(id, updateUserDto);
    return ApiResponseDto.success(result, 'User updated successfully');
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete user' })
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
    return ApiResponseDto.success(null, 'User deleted successfully');
  }

  // Microservice message patterns
  @MessagePattern('get_users')
  async handleGetUsers(data: any) {
    return this.usersService.findAll(data);
  }

  @MessagePattern('get_user_by_id')
  async handleGetUserById(data: { id: string }) {
    return this.usersService.findOne(data.id);
  }

  @MessagePattern('create_user')
  async handleCreateUser(data: CreateUserDto) {
    const command = new CreateUserCommand(data);
    return this.commandBus.execute(command);
  }
}
