export interface MicroserviceConfig {
  name: string;
  port: number;
  host?: string;
  version: string;
  environment: string;
  database?: DatabaseConfig;
  eventStore?: EventStoreConfig;
  redis?: RedisConfig;
}

export interface DatabaseConfig {
  type: 'postgres' | 'mysql' | 'mongodb';
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export interface EventStoreConfig {
  connectionString: string;
  credentials?: {
    username: string;
    password: string;
  };
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Date;
  details?: Record<string, any>;
}
