import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CqrsModule } from '@nestjs/cqrs';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { UserCreatedHandler } from './handlers/user-created.handler';
import { CreateUserHandler } from './commands/handlers/create-user.handler';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    CqrsModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    CreateUserHandler,
    UserCreatedHandler,
  ],
  exports: [UsersService],
})
export class UsersModule {}
