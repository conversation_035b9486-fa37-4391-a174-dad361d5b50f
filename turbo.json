{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "test": {"outputs": ["coverage/**"], "dependsOn": ["build"]}, "test:e2e": {"outputs": [], "dependsOn": ["build"]}, "clean": {"cache": false}}}