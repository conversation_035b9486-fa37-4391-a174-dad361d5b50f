import { Injectable, Inject, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Request } from 'express';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);

  constructor(
    @Inject('MANAGEMENT_SERVICE') private managementClient: ClientProxy,
    @Inject('CORRESPONDENCE_SERVICE') private correspondenceClient: ClientProxy,
  ) {}

  async forwardToService(serviceName: string, request: Request) {
    try {
      const client = this.getClient(serviceName);
      const pattern = this.buildPattern(request);
      const data = this.buildRequestData(request);

      this.logger.log(`Forwarding ${request.method} ${request.url} to ${serviceName}`);

      const result = await firstValueFrom(client.send(pattern, data));
      return result;
    } catch (error) {
      this.logger.error(`Error forwarding request to ${serviceName}:`, error);
      throw error;
    }
  }

  private getClient(serviceName: string): ClientProxy {
    switch (serviceName) {
      case 'MANAGEMENT_SERVICE':
        return this.managementClient;
      case 'CORRESPONDENCE_SERVICE':
        return this.correspondenceClient;
      default:
        throw new Error(`Unknown service: ${serviceName}`);
    }
  }

  private buildPattern(request: Request): string {
    const method = request.method.toLowerCase();
    const path = request.path.replace(/^\/api\/v1\//, '');
    return `${method}_${path.replace(/\//g, '_')}`;
  }

  private buildRequestData(request: Request) {
    return {
      method: request.method,
      url: request.url,
      path: request.path,
      query: request.query,
      body: request.body,
      headers: request.headers,
      user: (request as any).user,
    };
  }
}
