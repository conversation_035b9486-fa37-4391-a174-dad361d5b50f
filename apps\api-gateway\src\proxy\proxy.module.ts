import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ProxyController } from './proxy.controller';
import { ProxyService } from './proxy.service';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'MANAGEMENT_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.MANAGEMENT_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.MANAGEMENT_SERVICE_PORT) || 3001,
        },
      },
      {
        name: 'CORRESPONDENCE_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.CORRESPONDENCE_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.CORRESPONDENCE_SERVICE_PORT) || 3002,
        },
      },
    ]),
  ],
  controllers: [ProxyController],
  providers: [ProxyService],
})
export class ProxyModule {}
