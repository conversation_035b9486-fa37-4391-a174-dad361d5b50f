import { DomainEvent } from '@enterprise/common';
import { v4 as uuidv4 } from 'uuid';

export abstract class AggregateRoot {
  protected id: string;
  protected version: number = -1;
  private uncommittedEvents: DomainEvent[] = [];

  constructor(id?: string) {
    this.id = id || uuidv4();
  }

  getId(): string {
    return this.id;
  }

  getVersion(): number {
    return this.version;
  }

  getUncommittedEvents(): DomainEvent[] {
    return [...this.uncommittedEvents];
  }

  markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }

  loadFromHistory(events: DomainEvent[]): void {
    events.forEach(event => {
      this.applyEvent(event, false);
      this.version = event.eventVersion;
    });
  }

  protected applyEvent(event: DomainEvent, isNew: boolean = true): void {
    const handler = this.getEventHandler(event.eventType);
    if (handler) {
      handler.call(this, event.eventData);
    }

    if (isNew) {
      this.version++;
      event.eventVersion = this.version;
      event.aggregateId = this.id;
      event.aggregateType = this.constructor.name;
      event.occurredAt = new Date();
      this.uncommittedEvents.push(event);
    }
  }

  protected raiseEvent(eventType: string, eventData: any, metadata?: Record<string, any>): void {
    const event: DomainEvent = {
      eventId: uuidv4(),
      eventType,
      aggregateId: this.id,
      aggregateType: this.constructor.name,
      eventVersion: this.version + 1,
      eventData,
      metadata,
      occurredAt: new Date()
    };

    this.applyEvent(event);
  }

  private getEventHandler(eventType: string): Function | undefined {
    const handlerName = `on${eventType}`;
    return (this as any)[handlerName];
  }
}
