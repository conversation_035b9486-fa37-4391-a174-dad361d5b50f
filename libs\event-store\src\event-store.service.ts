import { Injectable, Logger } from '@nestjs/common';
import { EventStoreDBClient, jsonEvent, FORWARDS, START } from '@eventstore/db-client';
import { DomainEvent, EventStore } from '@enterprise/common';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class EventStoreService implements EventStore {
  private readonly logger = new Logger(EventStoreService.name);
  private client: EventStoreDBClient;

  constructor() {
    this.client = EventStoreDBClient.connectionString(
      process.env.EVENTSTORE_CONNECTION_STRING || 'esdb://localhost:2113?tls=false'
    );
  }

  async saveEvents(
    aggregateId: string,
    events: DomainEvent[],
    expectedVersion: number
  ): Promise<void> {
    try {
      const streamName = this.getStreamName(aggregateId);
      const eventData = events.map(event => 
        jsonEvent({
          id: uuidv4(),
          type: event.eventType,
          data: {
            ...event.eventData,
            aggregateId: event.aggregateId,
            aggregateType: event.aggregateType,
            eventVersion: event.eventVersion,
            occurredAt: event.occurredAt,
            metadata: event.metadata
          }
        })
      );

      await this.client.appendToStream(streamName, eventData, {
        expectedRevision: expectedVersion === -1 ? 'no_stream' : BigInt(expectedVersion)
      });

      this.logger.log(`Saved ${events.length} events for aggregate ${aggregateId}`);
    } catch (error) {
      this.logger.error(`Failed to save events for aggregate ${aggregateId}`, error);
      throw error;
    }
  }

  async getEvents(aggregateId: string, fromVersion?: number): Promise<DomainEvent[]> {
    try {
      const streamName = this.getStreamName(aggregateId);
      const events: DomainEvent[] = [];

      const readStream = this.client.readStream(streamName, {
        direction: FORWARDS,
        fromRevision: fromVersion ? BigInt(fromVersion) : START
      });

      for await (const resolvedEvent of readStream) {
        if (resolvedEvent.event) {
          const event = this.mapToDomainEvent(resolvedEvent.event);
          events.push(event);
        }
      }

      return events;
    } catch (error) {
      this.logger.error(`Failed to get events for aggregate ${aggregateId}`, error);
      throw error;
    }
  }

  async getAllEvents(fromPosition?: number): Promise<DomainEvent[]> {
    try {
      const events: DomainEvent[] = [];
      const readAll = this.client.readAll({
        direction: FORWARDS,
        fromPosition: fromPosition ? BigInt(fromPosition) : START
      });

      for await (const resolvedEvent of readAll) {
        if (resolvedEvent.event && !resolvedEvent.event.streamId.startsWith('$')) {
          const event = this.mapToDomainEvent(resolvedEvent.event);
          events.push(event);
        }
      }

      return events;
    } catch (error) {
      this.logger.error('Failed to get all events', error);
      throw error;
    }
  }

  private getStreamName(aggregateId: string): string {
    return `aggregate-${aggregateId}`;
  }

  private mapToDomainEvent(eventData: any): DomainEvent {
    return {
      eventId: eventData.id,
      eventType: eventData.type,
      aggregateId: eventData.data.aggregateId,
      aggregateType: eventData.data.aggregateType,
      eventVersion: eventData.data.eventVersion,
      eventData: eventData.data,
      metadata: eventData.data.metadata,
      occurredAt: new Date(eventData.data.occurredAt)
    };
  }
}
