import { IsUUID, IsDateString, IsOptional } from 'class-validator';

export class BaseDto {
  @IsUUID()
  id: string;

  @IsDateString()
  createdAt: Date;

  @IsDateString()
  updatedAt: Date;

  @IsOptional()
  @IsDateString()
  deletedAt?: Date;
}

export class CreateBaseDto {
  // Base properties for creation DTOs
}

export class UpdateBaseDto {
  @IsOptional()
  @IsDateString()
  updatedAt?: Date;
}

export class PaginationDto {
  @IsOptional()
  page?: number = 1;

  @IsOptional()
  limit?: number = 10;

  @IsOptional()
  sortBy?: string;

  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
}

export class PaginatedResponseDto<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
