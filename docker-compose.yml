version: '3.8'

services:
  # EventStore Database
  eventstore:
    image: eventstore/eventstore:23.10.0-bookworm-slim
    environment:
      - EVENTSTORE_CLUSTER_SIZE=1
      - EVENTSTORE_RUN_PROJECTIONS=All
      - EVENTSTORE_START_STANDARD_PROJECTIONS=true
      - EVENTSTORE_EXT_TCP_PORT=1113
      - EVENTSTORE_HTTP_PORT=2113
      - EVENTSTORE_INSECURE=true
      - EVENTSTORE_ENABLE_EXTERNAL_TCP=true
      - EVENTSTORE_ENABLE_ATOM_PUB_OVER_HTTP=true
    ports:
      - "1113:1113"
      - "2113:2113"
    volumes:
      - eventstore-volume-data:/var/lib/eventstore
      - eventstore-volume-logs:/var/log/eventstore
    networks:
      - enterprise-network

  # PostgreSQL for Management Center
  postgres-management:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: management_center
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres-management-data:/var/lib/postgresql/data
    networks:
      - enterprise-network

  # PostgreSQL for Correspondence
  postgres-correspondence:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: correspondence
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres-correspondence-data:/var/lib/postgresql/data
    networks:
      - enterprise-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - enterprise-network

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=your-super-secret-jwt-key
      - MANAGEMENT_SERVICE_HOST=management-center
      - MANAGEMENT_SERVICE_PORT=3001
      - CORRESPONDENCE_SERVICE_HOST=correspondence
      - CORRESPONDENCE_SERVICE_PORT=3002
    depends_on:
      - management-center
      - correspondence
    networks:
      - enterprise-network

  # Management Center Service
  management-center:
    build:
      context: .
      dockerfile: apps/management-center/Dockerfile
    ports:
      - "4001:4001"
    environment:
      - NODE_ENV=development
      - PORT=4001
      - MICROSERVICE_PORT=3001
      - DB_HOST=postgres-management
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=management_center
      - EVENTSTORE_CONNECTION_STRING=esdb://eventstore:2113?tls=false
    depends_on:
      - postgres-management
      - eventstore
    networks:
      - enterprise-network

  # Correspondence Service
  correspondence:
    build:
      context: .
      dockerfile: apps/correspondence/Dockerfile
    ports:
      - "4002:4002"
    environment:
      - NODE_ENV=development
      - PORT=4002
      - MICROSERVICE_PORT=3002
      - DB_HOST=postgres-correspondence
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=correspondence
      - EVENTSTORE_CONNECTION_STRING=esdb://eventstore:2113?tls=false
    depends_on:
      - postgres-correspondence
      - eventstore
    networks:
      - enterprise-network

volumes:
  eventstore-volume-data:
  eventstore-volume-logs:
  postgres-management-data:
  postgres-correspondence-data:
  redis-data:

networks:
  enterprise-network:
    driver: bridge
