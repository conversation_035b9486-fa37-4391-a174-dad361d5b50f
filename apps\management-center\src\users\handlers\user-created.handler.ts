import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { UserCreatedEvent } from '../events/user-created.event';
import { EventStoreService } from '@enterprise/event-store';
import { DomainEvent } from '@enterprise/common';
import { v4 as uuidv4 } from 'uuid';

@EventsHandler(UserCreatedEvent)
@Injectable()
export class UserCreatedHandler implements IEventHandler<UserCreatedEvent> {
  private readonly logger = new Logger(UserCreatedHandler.name);

  constructor(private readonly eventStore: EventStoreService) {}

  async handle(event: UserCreatedEvent) {
    this.logger.log(`Handling UserCreatedEvent for user: ${event.userId}`);

    // Create domain event for EventStore
    const domainEvent: DomainEvent = {
      eventId: uuidv4(),
      eventType: 'UserCreated',
      aggregateId: event.userId,
      aggregateType: 'User',
      eventVersion: 1,
      eventData: {
        userId: event.userId,
        email: event.email,
        firstName: event.firstName,
        lastName: event.lastName,
        role: event.role,
        organizationId: event.organizationId,
      },
      metadata: {
        source: 'management-center',
        timestamp: new Date().toISOString(),
      },
      occurredAt: new Date(),
    };

    // Save to EventStore
    try {
      await this.eventStore.saveEvents(event.userId, [domainEvent], -1);
      this.logger.log(`UserCreated event saved to EventStore for user: ${event.userId}`);
    } catch (error) {
      this.logger.error(`Failed to save UserCreated event to EventStore:`, error);
    }

    // Here you could also:
    // - Send welcome email
    // - Create user profile in other services
    // - Update analytics
    // - etc.
  }
}
