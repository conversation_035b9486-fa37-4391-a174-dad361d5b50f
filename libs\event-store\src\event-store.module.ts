import { Module, DynamicModule } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { EventStoreService } from './event-store.service';
import { EventStorePublisher } from './event-publisher';

export interface EventStoreModuleOptions {
  connectionString?: string;
}

@Module({})
export class EventStoreModule {
  static forRoot(options?: EventStoreModuleOptions): DynamicModule {
    return {
      module: EventStoreModule,
      imports: [CqrsModule],
      providers: [
        EventStoreService,
        EventStorePublisher,
        {
          provide: 'EVENTSTORE_OPTIONS',
          useValue: options || {}
        }
      ],
      exports: [EventStoreService, EventStorePublisher]
    };
  }
}
