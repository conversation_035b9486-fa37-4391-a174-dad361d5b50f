{"name": "@enterprise/common", "version": "1.0.0", "description": "Common utilities and types for enterprise microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/uuid": "^9.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "typescript": "^5.1.3"}}