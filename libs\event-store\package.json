{"name": "@enterprise/event-store", "version": "1.0.0", "description": "EventStore integration library for enterprise microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"@enterprise/common": "^1.0.0", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/cqrs": "^10.0.0", "@eventstore/db-client": "^6.0.0", "uuid": "^9.0.0", "rxjs": "^7.8.0"}, "devDependencies": {"@types/uuid": "^9.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "typescript": "^5.1.3"}}