export class ApiResponseDto<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  timestamp: Date;

  constructor(success: boolean, data?: T, message?: string, error?: string) {
    this.success = success;
    this.data = data;
    this.message = message;
    this.error = error;
    this.timestamp = new Date();
  }

  static success<T>(data?: T, message?: string): ApiResponseDto<T> {
    return new ApiResponseDto(true, data, message);
  }

  static error(error: string, message?: string): ApiResponseDto {
    return new ApiResponseDto(false, undefined, message, error);
  }
}
