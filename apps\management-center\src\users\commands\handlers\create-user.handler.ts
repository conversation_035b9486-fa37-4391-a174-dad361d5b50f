import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { CreateUserCommand } from '../create-user.command';
import { User } from '../../entities/user.entity';
import { UserCreatedEvent } from '../../events/user-created.event';

@CommandHandler(CreateUserCommand)
@Injectable()
export class CreateUserHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private eventBus: EventBus,
  ) {}

  async execute(command: CreateUserCommand): Promise<User> {
    const { createUserDto } = command;

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Create user entity
    const user = this.userRepository.create({
      id: uuidv4(),
      ...createUserDto,
      password: hashedPassword,
    });

    // Save user
    const savedUser = await this.userRepository.save(user);

    // Publish domain event
    const event = new UserCreatedEvent(
      savedUser.id,
      savedUser.email,
      savedUser.firstName,
      savedUser.lastName,
      savedUser.role,
      savedUser.organizationId,
    );

    this.eventBus.publish(event);

    return savedUser;
  }
}
